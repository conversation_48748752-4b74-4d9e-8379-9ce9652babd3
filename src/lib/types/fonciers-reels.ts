import type { FormDataInvestementAnalysistSteps } from './investement-analysis.tsx';

export interface ProjectionDataItem {
  annee: number;
  revenusLocatifsHorsCharges: number;
  travaux: number;
  taxeFonciere: number;
  chargesLocatives: number;
  interetsEmprunts: number;
  fraisEmprunts: number;
  fraisAgences: number;
  primesAssurances: number;
  resultatFonciers: number;
  resultatFonciersCumulatifs: number;
  plafondDeficitImputable: number;
  resultatFonciersReportables: number;
  tauxMarginalImposition: number;
  prelevementsSociaux: number;
  csgDeductible: number;
  irpp: number;
  tresorerieDegageeHorsEmprunts: number;
  remboursementsEmprunts: number;
  tresorerieTotaleDegagee: number;
  tresorerieTotaleDegageeAvecEcoIR: number;
  tresorerieTotalCumul: number;
  totalTresorerieGeneree?: number;
  limitExceeded?: boolean;
  limitExceededYear?: number;
}

export interface PlusValueDataItem {
  annee: number;
  prixVente: number;
  augmentationPrixVente: number;
  prixAchat: number;
  augmentationPrixAcquisition: number;
  plusValue: number;
  nombreParts: number;
  plusValueParPart: number;
  tauxAbattementImpotRevenu: number;
  abattementImpotRevenu: number;
  baseTaxableImpotRevenu: number;
  baseTaxableImpotRevenuParPart: number;
  impotRevenu: number;
  tauxAbattementPrelevementsSociaux: number;
  abattementPrelevementsSociaux: number;
  baseTaxablePrelevementsSociaux: number;
  prelevementsSociauxPlusValue: number;
  taxationSupplementaire: number;
  totalImpositionPlusValue: number;
  totalTresorerieGeneree: number;
}

export interface SCIPartsDataItem {
  annee: number;
  valeurTheoriqueActifReel: number;
  tresorerieDisponible: number;
  emprunt: number;
  compteCourant: number;
  prixCessionPartsTheorique: number;
  augmentationPrixVente: number;
  valeurOriginePartsSociales: number;
  priseEnCompteGainsFiscaux: number;
  plusValue: number;
  nombreParts: number;
  plusValueParPart: number;
  tauxAbattementImpotRevenu: number;
  abattementImpotRevenu: number;
  baseTaxableImpotRevenu: number;
  baseTaxableImpotRevenuParPart: number;
  impotRevenu: number;
  tauxAbattementPrelevementsSociaux: number;
  abattementPrelevementsSociaux: number;
  baseTaxablePrelevementsSociaux: number;
  prelevementsSociauxPlusValue: number;
  taxationSupplementaire: number;
  totalImpositionPlusValue: number;
}

export interface FonciersReelsFormData {
  // Conditions
  locationsNues: string;
  loyersAnnuels: number;
  autresRevenusLocatifsNue: string;
  microFonciers: number;
  foncierReels: number;
  regimeFonciersReelPossible: boolean;
  concatener: string;
  regimeFonciersReelApplicable: boolean;
  cfeApplicable: boolean;

  // Projections
  indiceMoyen: number;
  projectionData: ProjectionDataItem[];
  limitExceededYear?: number;

  // Plus-value
  plusValueData: PlusValueDataItem[];

  // SCI Parts
  sciPartsData: SCIPartsDataItem[];
}

export interface FonciersReelsFormProps {
  data: FormDataInvestementAnalysistSteps;
  setParentFormData: (data: Partial<FonciersReelsFormData>) => void;
}
